import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/report/mixin/report_mixin.dart';
import 'package:koc_app/src/shared/widgets/charts/chart_constants.dart';
import 'package:koc_app/src/shared/widgets/charts/chart_models.dart';

/// A reusable campaign chart widget with consistent styling across the app
class Campaign<PERSON><PERSON> extends StatelessWidget with ReportMixin {
  const CampaignChart({
    super.key,
    required this.data,
    this.config = const CampaignChartConfig(),
    this.height,
    this.showNoDataMessage = true,
  });

  final CampaignChartData data;
  final CampaignChartConfig config;
  final double? height;
  final bool showNoDataMessage;

  @override
  Widget build(BuildContext context) {
    if (data.items.isEmpty && showNoDataMessage) {
      return _buildNoDataWidget(context);
    }

    Widget chartContent = Column(
      children: [
        if (config.showTitle && config.title.isNotEmpty) _buildTitle(context),
        if (config.showLegend && config.legendPosition == LegendPosition.above) _buildLegend(context),
        if (config.showLegend && config.legendPosition == LegendPosition.above) SizedBox(height: 8.r),
        Expanded(
          child: _buildChart(context),
        ),
        if (config.showLegend && config.legendPosition == LegendPosition.below) SizedBox(height: 8.r),
        if (config.showLegend && config.legendPosition == LegendPosition.below) _buildLegend(context),
      ],
    );

    Widget containerContent = Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: config.borderColor ?? const Color(0xFFE7E7E7),
          width: 1.r,
        ),
        color: config.backgroundColor,
      ),
      padding: config.padding ?? EdgeInsets.symmetric(vertical: 4.r),
      margin: config.margin,
      child: chartContent,
    );

    if (height != null) {
      return SizedBox(
        height: height,
        child: containerContent,
      );
    }

    return containerContent;
  }

  Widget _buildNoDataWidget(BuildContext context) {
    final double emptyStateMaxValue = config.emptyStateMaxValue;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: config.borderColor ?? const Color(0xFFE7E7E7),
          width: 1.r,
        ),
        color: config.backgroundColor,
      ),
      padding: config.padding ?? EdgeInsets.all(12.r),
      child: Column(
        children: [
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(right: 12.r, top: 12.r, bottom: 8.r),
              child: AspectRatio(
                aspectRatio: config.aspectRatio,
                child: Column(
                  children: [
                    Expanded(
                      child: Stack(
                        children: [
                          if (config.showGrid)
                            Positioned(
                              left: 88.r,
                              right: 0,
                              top: 0,
                              bottom: 0,
                              child: CustomPaint(
                                painter: GridLinePainter(
                                  verticalLineCount: config.yAxisGridLines,
                                  horizontalLineCount: 5,
                                  color: Colors.grey[300]!,
                                  strokeWidth: 0.8,
                                ),
                              ),
                            ),
                          Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.bar_chart_outlined, size: 32.r, color: Colors.grey[400]),
                                SizedBox(height: 4.r),
                                Text(
                                  config.emptyStateMessage,
                                  style: config.titleStyle ??
                                      Theme.of(context).textTheme.labelMedium?.copyWith(
                                            color: Colors.grey[600],
                                            fontSize: 12.sp,
                                          ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 8.r),
                    Row(
                      children: [
                        SizedBox(width: 80.r),
                        Expanded(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: List.generate(config.yAxisGridLines + 1, (index) {
                              final value = (emptyStateMaxValue / config.yAxisGridLines) * index;
                              return Text(
                                formatChartNumber(value),
                                style: config.labelStyle ??
                                    Theme.of(context).textTheme.labelSmall?.copyWith(
                                          fontSize: 10.sp,
                                          color: Colors.grey[500],
                                        ),
                              );
                            }),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 12.r),
      child: Text(
        config.title,
        style: config.titleStyle ??
            Theme.of(context).textTheme.labelMedium!.copyWith(
                  fontWeight: FontWeight.w500,
                ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildLegend(BuildContext context) {
    if (data.items.isEmpty) return const SizedBox.shrink();

    return Wrap(
      spacing: 16.r,
      runSpacing: 8.r,
      alignment: WrapAlignment.center,
      children: data.items
          .take(5)
          .map((item) => _buildLegendItem(
                context,
                item.name,
                item.color ?? config.primaryColor ?? ChartConstants.leftLineColor,
              ))
          .toList(),
    );
  }

  Widget _buildLegendItem(BuildContext context, String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 8.r,
          height: 8.r,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        SizedBox(width: 4.r),
        Text(
          label,
          style: config.labelStyle ??
              Theme.of(context).textTheme.labelSmall?.copyWith(
                    fontSize: 10.sp,
                  ),
        ),
      ],
    );
  }

  Widget _buildChart(BuildContext context) {
    final chartData = _prepareChartData();

    if (chartData.isEmpty) {
      return _buildNoDataWidget(context);
    }

    switch (config.chartType) {
      case CampaignChartType.horizontalBar:
        return _buildHorizontalBarChart(context, chartData);
      case CampaignChartType.line:
        return _buildLineChart(context, chartData);
    }
  }

  List<Map<String, dynamic>> _prepareChartData() {
    List<CampaignChartItem> sortedItems = List.from(data.items);

    if (config.maxItems > 0 && sortedItems.length > config.maxItems) {
      sortedItems = sortedItems.take(config.maxItems).toList();
    }

    sortedItems.sort((a, b) => config.sortAscending ? a.value.compareTo(b.value) : b.value.compareTo(a.value));

    return sortedItems
        .map((item) => {
              'name': item.name,
              'value': item.value,
              'id': item.id,
            })
        .toList();
  }

  Widget _buildHorizontalBarChart(BuildContext context, List<Map<String, dynamic>> chartData) {
    final maxValue = chartData.isNotEmpty ? chartData.map((e) => e['value'] as num).reduce((a, b) => a > b ? a : b) : 0;
    final chartMaxValue = maxValue > 0 ? maxValue.toDouble() : 10.0;

    final processedData = chartData.map((item) {
      final name = item['name'] as String;
      final truncatedName = name.length > config.maxLabelLength
          ? '${name.substring(0, config.maxLabelLength)}${config.labelTruncationSuffix}'
          : name;
      return {
        'name': truncatedName,
        'originalName': name,
        'value': item['value'],
      };
    }).toList();

    processedData.sort((a, b) => (b['value'] as num).compareTo(a['value'] as num));

    return Padding(
      padding: EdgeInsets.only(right: 12.r, top: 12.r, bottom: 8.r),
      child: AspectRatio(
        aspectRatio: config.aspectRatio,
        child: Column(
          children: [
            Expanded(
              child: Stack(
                children: [
                  if (config.showGrid)
                    Positioned(
                      left: 88.r,
                      right: 0,
                      top: 0,
                      bottom: 0,
                      child: Builder(
                        builder: (context) {
                          return CustomPaint(
                            painter: GridLinePainter(
                              verticalLineCount: config.yAxisGridLines,
                              horizontalLineCount: processedData.length - 1,
                              color: Colors.grey[300]!,
                              strokeWidth: 0.5,
                            ),
                          );
                        },
                      ),
                    ),
                  Column(
                    children: List.generate(processedData.length, (index) {
                      final item = processedData[index];
                      final value = item['value'] as num;
                      final percentage = value / chartMaxValue;

                      return Expanded(
                        child: Padding(
                          padding: EdgeInsets.symmetric(vertical: 2.r),
                          child: Row(
                            children: [
                              // Campaign name (left side)
                              SizedBox(
                                width: 80.r,
                                child: Text(
                                  item['name'] as String,
                                  style: config.labelStyle ??
                                      Theme.of(context).textTheme.labelSmall?.copyWith(fontSize: 9.sp),
                                  textAlign: TextAlign.right,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              SizedBox(width: 8.r),
                              // Bar area
                              Expanded(
                                child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: FractionallySizedBox(
                                    alignment: Alignment.centerLeft,
                                    widthFactor: percentage.clamp(0.0, 1.0),
                                    child: Container(
                                      height: 12.r,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(2.r),
                                        color: config.primaryColor ?? ChartConstants.leftLineColor,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }),
                  ),
                ],
              ),
            ),
            SizedBox(height: 8.r),
            Row(
              children: [
                SizedBox(width: 80.r),
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: List.generate(config.yAxisGridLines + 1, (index) {
                      final value = (chartMaxValue / config.yAxisGridLines) * index;
                      return Text(
                        formatChartNumber(value),
                        style: config.labelStyle ?? Theme.of(context).textTheme.labelSmall?.copyWith(fontSize: 10.sp),
                      );
                    }),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLineChart(BuildContext context, List<Map<String, dynamic>> chartData) {
    if (chartData.isEmpty) {
      return _buildNoDataWidget(context);
    }

    final maxValue = chartData.map((e) => e['value'] as num).reduce((a, b) => a > b ? a : b);
    final chartMaxValue = maxValue > 0 ? maxValue.toDouble() : 10.0;

    final spots = List.generate(chartData.length, (index) {
      return FlSpot(index.toDouble(), (chartData[index]['value'] as num).toDouble());
    });

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 8.r),
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: config.showGrid,
            drawVerticalLine: false,
            horizontalInterval: _calculateGridInterval(chartMaxValue, config.yAxisGridLines),
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: Colors.grey[300]!,
                strokeWidth: 0.8, // Slightly thicker for better visibility
              );
            },
          ),
          titlesData: FlTitlesData(
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40.r,
                getTitlesWidget: (value, meta) {
                  return Text(
                    formatChartNumber(value),
                    style: config.labelStyle ?? Theme.of(context).textTheme.labelSmall?.copyWith(fontSize: 10.sp),
                  );
                },
              ),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40.r,
                getTitlesWidget: (value, meta) {
                  final index = value.toInt();
                  if (index >= 0 && index < chartData.length) {
                    final name = chartData[index]['name'] as String;
                    return Padding(
                      padding: EdgeInsets.only(top: 8.r),
                      child: Transform.rotate(
                        angle: config.rotateLabels ? ChartConstants.xAxisLabelRotation : 0,
                        child: Text(
                          name.length > config.maxLabelLength
                              ? '${name.substring(0, config.maxLabelLength)}${config.labelTruncationSuffix}'
                              : name,
                          style: config.labelStyle ?? Theme.of(context).textTheme.labelSmall?.copyWith(fontSize: 9.sp),
                        ),
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ),
            rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: ChartConstants.showBorder),
          minY: 0,
          maxY: chartMaxValue,
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              isCurved: ChartConstants.showCurvedLines,
              color: config.primaryColor ?? ChartConstants.leftLineColor,
              barWidth: ChartConstants.lineWidth,
              dotData: FlDotData(
                show: ChartConstants.showDots,
                getDotPainter: (spot, percent, barData, index) {
                  return FlDotCirclePainter(
                    radius: ChartConstants.dotRadius,
                    color: config.primaryColor ?? ChartConstants.leftLineColor,
                    strokeWidth: ChartConstants.dotStrokeWidth,
                    strokeColor: ChartConstants.dotStrokeColor,
                  );
                },
              ),
              belowBarData: BarAreaData(
                show: ChartConstants.showAreaFill,
                color: (config.primaryColor ?? ChartConstants.leftLineColor)
                    .withValues(alpha: ChartConstants.areaFillOpacity),
              ),
            ),
          ],
          lineTouchData: LineTouchData(
            enabled: config.showTooltip,
            touchTooltipData: LineTouchTooltipData(
              getTooltipColor: (touchedSpot) => config.backgroundColor ?? ChartConstants.tooltipBackgroundColor,
              getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                return touchedBarSpots
                    .map((barSpot) {
                      final index = barSpot.x.toInt();
                      if (index >= 0 && index < chartData.length) {
                        final name = chartData[index]['name'] as String;
                        final value = formatTooltipNumber(barSpot.y, data.valueLabel);

                        return LineTooltipItem(
                          '$name: $value',
                          TextStyle(
                            color: config.primaryColor ?? ChartConstants.leftLineColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 12.sp,
                          ),
                        );
                      }
                      return null;
                    })
                    .where((item) => item != null)
                    .cast<LineTooltipItem>()
                    .toList();
              },
            ),
          ),
        ),
      ),
    );
  }

  /// Calculate grid interval ensuring it's never 0 or too small
  /// This prevents grid lines from disappearing when chartMaxValue is small
  double _calculateGridInterval(num chartMaxValue, int yAxisGridLines) {
    if (chartMaxValue <= 0 || yAxisGridLines <= 0) {
      return 1.0;
    }
    final interval = chartMaxValue / yAxisGridLines;
    final finalInterval = interval > 0 ? interval.toDouble() : 1.0;
    return finalInterval;
  }

  static CampaignChartData fromLists({
    required List<String> names,
    required List<num> values,
    List<int>? ids,
    List<Color>? colors,
    String valueLabel = 'Value',
    String nameLabel = 'Name',
  }) {
    assert(names.length == values.length, 'Names and values lists must have the same length');

    final items = List.generate(names.length, (index) {
      return CampaignChartItem(
        name: names[index],
        value: values[index],
        id: ids?.elementAtOrNull(index),
        color: colors?.elementAtOrNull(index),
      );
    });

    return CampaignChartData(
      items: items,
      valueLabel: valueLabel,
      nameLabel: nameLabel,
    );
  }

  /// Helper method to create campaign chart data from map data
  static CampaignChartData fromMapList({
    required List<Map<String, dynamic>> data,
    required String nameKey,
    required String valueKey,
    String? idKey,
    String? colorKey,
    String valueLabel = 'Value',
    String nameLabel = 'Name',
  }) {
    final items = data.map((item) {
      return CampaignChartItem(
        name: item[nameKey]?.toString() ?? '',
        value: item[valueKey] ?? 0,
        id: idKey != null ? item[idKey] : null,
        color: colorKey != null ? item[colorKey] : null,
      );
    }).toList();

    return CampaignChartData(
      items: items,
      valueLabel: valueLabel,
      nameLabel: nameLabel,
    );
  }
}

/// Custom painter for drawing grid lines in horizontal bar chart
class GridLinePainter extends CustomPainter {
  final int verticalLineCount;
  final int horizontalLineCount;
  final Color color;
  final double strokeWidth;

  GridLinePainter({
    required this.verticalLineCount,
    required this.horizontalLineCount,
    required this.color,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    // Draw vertical grid lines (for value axis)
    for (int i = 0; i <= verticalLineCount; i++) {
      final x = (size.width / verticalLineCount) * i;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // Draw horizontal grid lines (between bars)
    for (int i = 0; i <= horizontalLineCount; i++) {
      final y = (size.height / horizontalLineCount) * i;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // Debug: Print grid line info
    debugPrint(
        'GridLinePainter: Drawing ${verticalLineCount + 1} vertical and ${horizontalLineCount + 1} horizontal lines, size: ${size.width}x${size.height}, color: $color');
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is GridLinePainter) {
      return oldDelegate.verticalLineCount != verticalLineCount ||
          oldDelegate.horizontalLineCount != horizontalLineCount ||
          oldDelegate.color != color ||
          oldDelegate.strokeWidth != strokeWidth;
    }
    return true;
  }
}
